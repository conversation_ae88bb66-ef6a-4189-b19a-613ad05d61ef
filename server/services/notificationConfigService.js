const { NotificationConfig } = require('../models');
const feishuBotService = require('./feishuBotService');
const notificationLogService = require('./notificationLogService');
const axios = require('axios');

/**
 * 通知配置管理服务
 */
class NotificationConfigService {
  constructor() {
    // 通知类型映射
    this.notificationTypes = {
      'exchange_notification': '兑换申请通知',
      'stock_alert': '库存告警通知',
      'new_user_welcome': '新用户欢迎',
      'new_product_notification': '新品上架通知',
      'daily_report': '每日销售汇总',
      'weekly_report': '每周销售汇总',
      'monthly_report': '月度销售汇总',
      'order_alert': '异常订单预警',
      'maintenance_notification': '系统维护通知',
      'error_alert': '错误告警推送'
    };
  }

  /**
   * 获取所有通知配置
   */
  async getAllConfigs() {
    try {
      // 先确保所有通知类型都有配置记录
      await this.ensureAllConfigsExist();

      const configs = await NotificationConfig.findAll({
        order: [['notificationType', 'ASC']]
      });

      return configs.map(config => ({
        id: config.id,
        notificationType: config.notificationType,
        typeName: this.notificationTypes[config.notificationType] || config.notificationType,
        enabled: config.enabled,
        webhookUrl: config.webhookUrl,
        scheduleTime: config.scheduleTime,
        retryCount: config.retryCount,
        createdAt: config.createdAt,
        updatedAt: config.updatedAt
      }));
    } catch (error) {
      console.error('获取通知配置失败:', error);
      throw new Error('获取通知配置失败');
    }
  }

  /**
   * 确保所有通知类型都有配置记录
   */
  async ensureAllConfigsExist() {
    try {
      const allTypes = Object.keys(this.notificationTypes);
      const existingConfigs = await NotificationConfig.findAll({
        attributes: ['notificationType']
      });
      const existingTypes = existingConfigs.map(config => config.notificationType);

      const missingTypes = allTypes.filter(type => !existingTypes.includes(type));

      if (missingTypes.length > 0) {
        console.log('🔧 发现缺失的通知配置，正在创建:', missingTypes);

        const createPromises = missingTypes.map(notificationType => {
          const scheduleTime = ['daily_report', 'weekly_report', 'monthly_report'].includes(notificationType)
            ? (notificationType === 'daily_report' ? '19:00' : '09:00')
            : null;

          return NotificationConfig.create({
            notificationType,
            enabled: true,
            webhookUrl: process.env.FEISHU_BOT_WEBHOOK_URL,
            scheduleTime,
            retryCount: 3
          });
        });

        await Promise.all(createPromises);
        console.log('✅ 缺失的通知配置创建完成');
      }
    } catch (error) {
      console.error('确保通知配置存在失败:', error);
      // 不抛出错误，避免影响主流程
    }
  }

  /**
   * 根据类型获取通知配置
   */
  async getConfigByType(notificationType) {
    try {
      const config = await NotificationConfig.findOne({
        where: { notificationType }
      });

      if (!config) {
        return null;
      }

      return {
        id: config.id,
        notificationType: config.notificationType,
        typeName: this.notificationTypes[config.notificationType] || config.notificationType,
        enabled: config.enabled,
        webhookUrl: config.webhookUrl,
        scheduleTime: config.scheduleTime,
        retryCount: config.retryCount,
        createdAt: config.createdAt,
        updatedAt: config.updatedAt
      };
    } catch (error) {
      console.error('获取通知配置失败:', error);
      throw new Error('获取通知配置失败');
    }
  }

  /**
   * 更新通知配置
   */
  async updateConfig(notificationType, updateData) {
    try {
      console.log(`🔧 更新通知配置: ${notificationType}`, updateData);

      // 先检查配置是否存在
      let config = await NotificationConfig.findOne({
        where: { notificationType }
      });

      // 默认的webhook URL
      const defaultWebhookUrl = "https://open.feishu.cn/open-apis/bot/v2/hook/e6cff700-4172-4039-a700-43c8f43765fc";
      
      // 确保webhookUrl有值 - 优先使用提供的值，其次是已有配置中的值，然后是环境变量，最后是默认值
      let webhookUrl = updateData.webhookUrl;
      if (!webhookUrl) {
        // 如果没有提供webhookUrl，尝试使用已有配置的webhookUrl
        if (config && config.webhookUrl) {
          webhookUrl = config.webhookUrl;
          console.log(`🔧 使用已有配置的webhookUrl: ${webhookUrl.substring(0, 20)}...`);
        } else {
          // 尝试使用环境变量
          webhookUrl = process.env.FEISHU_BOT_WEBHOOK_URL || defaultWebhookUrl;
          console.log(`🔧 使用环境变量或默认的webhookUrl: ${webhookUrl.substring(0, 20)}...`);
        }
      } else {
        console.log(`🔧 使用提供的webhookUrl: ${webhookUrl.substring(0, 20)}...`);
      }
      
      // 确保updateData包含webhookUrl
      updateData.webhookUrl = webhookUrl;

      if (!config) {
        // 如果配置不存在，创建新配置
        console.log(`⚠️ 通知配置不存在，创建新配置: ${notificationType}`);
        config = await NotificationConfig.create({
          notificationType,
          enabled: updateData.enabled !== undefined ? updateData.enabled : true,
          webhookUrl: updateData.webhookUrl,
          retryCount: updateData.retryCount || 3,
          ...updateData
        });
        console.log(`✅ 创建新通知配置成功: ${notificationType}`);
      } else {
        // 更新现有配置
        await config.update(updateData);
        console.log(`✅ 更新通知配置成功: ${notificationType}`);
      }

      return await this.getConfigByType(notificationType);
    } catch (error) {
      console.error('更新通知配置失败:', error);
      throw new Error('更新通知配置失败: ' + error.message);
    }
  }

  /**
   * 批量更新通知配置
   */
  async batchUpdateConfigs(configsData) {
    try {
      const updatePromises = configsData.map(configData => {
        const { notificationType, ...updateData } = configData;
        return this.updateConfig(notificationType, updateData);
      });

      await Promise.all(updatePromises);
      return await this.getAllConfigs();
    } catch (error) {
      console.error('批量更新通知配置失败:', error);
      throw new Error('批量更新通知配置失败: ' + error.message);
    }
  }

  /**
   * 检查通知类型是否启用
   */
  async isNotificationEnabled(notificationType) {
    try {
      const config = await NotificationConfig.findOne({
        where: { notificationType }
      });

      return config ? config.enabled : false;
    } catch (error) {
      console.error('检查通知状态失败:', error);
      return false;
    }
  }

  /**
   * 测试webhook连接
   */
  async testWebhookConnection(webhookUrl = null) {
    try {
      const testUrl = webhookUrl || process.env.FEISHU_BOT_WEBHOOK_URL;
      
      if (!testUrl) {
        throw new Error('webhook地址未配置');
      }

      const testMessage = {
        msg_type: "text",
        content: {
          text: "🧪 飞书机器人连接测试\n\n这是一条测试消息，用于验证机器人连接是否正常。\n\n⏰ 测试时间：" + new Date().toLocaleString('zh-CN')
        }
      };

      const startTime = Date.now();
      const response = await axios.post(testUrl, testMessage, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000 // 10秒超时
      });

      const responseTime = Date.now() - startTime;

      console.log('Webhook测试响应:', response.data);

      return {
        success: true,
        responseTime,
        status: response.status,
        data: response.data,
        message: '连接测试成功'
      };
    } catch (error) {
      console.error('Webhook连接测试失败:', error);
      
      let errorMessage = '连接测试失败';
      if (error.code === 'ECONNREFUSED') {
        errorMessage = '连接被拒绝，请检查webhook地址';
      } else if (error.code === 'ETIMEDOUT') {
        errorMessage = '连接超时，请检查网络连接';
      } else if (error.response) {
        errorMessage = `服务器响应错误: ${error.response.status} ${error.response.statusText}`;
      }

      return {
        success: false,
        error: errorMessage,
        details: error.message
      };
    }
  }

  /**
   * 发送测试通知
   */
  async sendTestNotification(notificationType) {
    try {
      const config = await this.getConfigByType(notificationType);
      
      if (!config) {
        throw new Error('通知配置不存在');
      }

      if (!config.enabled) {
        throw new Error('通知功能已禁用');
      }

      let testResult;
      const typeName = this.notificationTypes[notificationType] || notificationType;
      const webhookUrl = config.webhookUrl || process.env.FEISHU_BOT_WEBHOOK_URL;

      switch (notificationType) {
        case 'daily_report':
          // 发送测试的每日报告
          const testDailyData = {
            date: new Date().toLocaleDateString('zh-CN'),
            weekday: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][new Date().getDay()],
            totalOrders: 8,
            lyOrders: 5,
            rmbOrders: 3,
            lyAmount: 350,
            rmbAmount: 125.5,
            totalQuantity: 12,
            uniqueUsers: 6,
            topProductsList: [
              { name: '测试商品1', quantity: 5 },
              { name: '测试商品2', quantity: 4 },
              { name: '测试商品3', quantity: 3 }
            ],
            topDepartments: [
              { name: '技术部', quantity: 7 },
              { name: '产品部', quantity: 3 },
              { name: '运营部', quantity: 2 }
            ]
          };
          
          // 使用日志记录的方式发送
          const dailyPayload = this.buildDailyReportPayload(testDailyData);
          testResult = await notificationLogService.sendWithLog({
            notificationType,
            webhookUrl,
            payload: dailyPayload,
            triggerSource: 'test',
            createdBy: null
          });
          break;

        case 'stock_alert':
          // 发送测试库存告警
          const testProduct = {
            name: '测试商品',
            stock: 5,
            lyPrice: 50,
            rmbPrice: 25,
            exchangeCount: 15
          };
          
          const stockPayload = this.buildStockAlertPayload(testProduct, 10);
          testResult = await notificationLogService.sendWithLog({
            notificationType,
            webhookUrl,
            payload: stockPayload,
            triggerSource: 'test',
            createdBy: null
          });
          break;

        case 'new_user_welcome':
          // 发送测试新用户欢迎
          const testUser = {
            username: '测试用户',
            departmentPath: '技术部/后端组',
            mobile: '138****8888',
            email: '<EMAIL>',
            createdAt: new Date()
          };
          
          const welcomePayload = this.buildUserWelcomePayload(testUser);
          testResult = await notificationLogService.sendWithLog({
            notificationType,
            webhookUrl,
            payload: welcomePayload,
            triggerSource: 'test',
            createdBy: null
          });
          break;

        default:
          // 发送通用测试消息
          const testMessage = {
            msg_type: "text",
            content: {
              text: `🧪 ${typeName} - 测试通知\n\n这是一条测试消息，用于验证【${typeName}】功能是否正常工作。\n\n⏰ 测试时间：${new Date().toLocaleString('zh-CN')}`
            }
          };
          
          testResult = await notificationLogService.sendWithLog({
            notificationType,
            webhookUrl,
            payload: testMessage,
            triggerSource: 'test',
            createdBy: null
          });
          break;
      }

      return {
        success: testResult.success,
        message: `${typeName}测试通知${testResult.success ? '发送成功' : '发送失败'}`,
        data: testResult
      };
    } catch (error) {
      console.error('发送测试通知失败:', error);
      return {
        success: false,
        message: `发送测试通知失败: ${error.message}`,
        error: error.message
      };
    }
  }

  /**
   * 构建每日报告负载
   */
  buildDailyReportPayload(reportData) {
    return {
      msg_type: "interactive",
      card: {
        elements: [
          {
            tag: "div",
            text: {
              content: `📊 **今日销售汇总报告**\n\n` +
                      `**日期**: ${reportData.date} (${reportData.weekday})\n` +
                      `**订单总数**: ${reportData.totalOrders} 单\n` +
                      `　└ 光年币: ${reportData.lyOrders} 单 (${reportData.lyAmount} 光年币)\n` +
                      `　└ 人民币: ${reportData.rmbOrders} 单 (¥${reportData.rmbAmount})\n` +
                      `**商品总量**: ${reportData.totalQuantity} 件\n` +
                      `**参与用户**: ${reportData.uniqueUsers} 人\n\n` +
                      `**热门商品TOP3**:\n` +
                      reportData.topProductsList.map((item, index) => 
                        `${index + 1}. ${item.name} (${item.quantity}件)`
                      ).join('\n') + '\n\n' +
                      `**活跃部门TOP3**:\n` +
                      reportData.topDepartments.map((dept, index) => 
                        `${index + 1}. ${dept.name} (${dept.quantity}件)`
                      ).join('\n'),
              tag: "lark_md"
            }
          }
        ],
        header: {
          title: {
            content: `🛍️ 光年小卖部 - 每日销售汇总`,
            tag: "plain_text"
          },
          template: "blue"
        }
      }
    };
  }

  /**
   * 构建库存告警负载
   */
  buildStockAlertPayload(product, alertThreshold) {
    let statusIcon = '⚠️';
    let statusText = '库存不足';
    let templateColor = 'orange';

    if (product.stock === 0) {
      statusIcon = '🚫';
      statusText = '库存耗尽';
      templateColor = 'red';
    } else if (product.stock <= alertThreshold * 0.5) {
      statusIcon = '🔴';
      statusText = '库存紧急';
      templateColor = 'red';
    }

    return {
      msg_type: "interactive",
      card: {
        elements: [
          {
            tag: "div",
            text: {
              content: `${statusIcon} **库存告警通知**\n\n` +
                      `**商品名称**: ${product.name}\n` +
                      `**当前库存**: ${product.stock} 个\n` +
                      `**告警阈值**: ${alertThreshold} 个\n` +
                      `**状态**: ${statusText}\n` +
                      `**光年币价格**: ${product.lyPrice} 光年币\n` +
                      `**人民币价格**: ¥${product.rmbPrice}\n` +
                      `**已兑换数量**: ${product.exchangeCount || 0} 个\n` +
                      `**告警时间**: ${new Date().toLocaleString('zh-CN')}\n\n` +
                      `💡 **建议**: ${product.stock === 0 ? '请立即补充库存' : '请考虑及时补充库存以满足兑换需求'}`,
              tag: "lark_md"
            }
          }
        ],
        header: {
          title: {
            content: `📦 光年小卖部 - 库存告警`,
            tag: "plain_text"
          },
          template: templateColor
        }
      }
    };
  }

  /**
   * 构建用户欢迎负载
   */
  buildUserWelcomePayload(user) {
    return {
      msg_type: "interactive",
      card: {
        elements: [
          {
            tag: "div",
            text: {
              content: `🎉 **欢迎新同事加入光年小卖部！**\n\n` +
                      `**姓名**: ${user.username}\n` +
                      `**部门**: ${user.departmentPath || '未知部门'}\n` +
                      `**注册时间**: ${new Date(user.createdAt).toLocaleString('zh-CN')}\n\n` +
                      `🛍️ 光年小卖部为您提供丰富的福利商品，支持光年币和人民币兑换。快来挑选您喜欢的商品吧！\n\n` +
                      `💡 **小贴士**:\n` +
                      `• 每天都有新品上架，记得常来看看\n` +
                      `• 光年币兑换更优惠，人民币直接购买\n` +
                      `• 有任何问题可以联系管理员`,
              tag: "lark_md"
            }
          }
        ],
        header: {
          title: {
            content: `🎊 光年小卖部 - 新用户欢迎`,
            tag: "plain_text"
          },
          template: "green"
        }
      }
    };
  }

  /**
   * 发送自定义消息
   */
  async sendCustomMessage(content, messageType = 'text') {
    try {
      let messageData;

      if (messageType === 'text') {
        messageData = {
          msg_type: "text",
          content: {
            text: content
          }
        };
      } else {
        // 可以扩展支持其他消息类型
        throw new Error('暂不支持该消息类型');
      }

      // 使用日志记录的方式发送
      const result = await notificationLogService.sendWithLog({
        notificationType: 'custom_message',
        webhookUrl: process.env.FEISHU_BOT_WEBHOOK_URL,
        payload: messageData,
        triggerSource: 'manual',
        createdBy: null
      });

      return {
        success: result.success,
        message: result.success ? '自定义消息发送成功' : `发送自定义消息失败: ${result.errorMessage}`,
        data: result
      };
    } catch (error) {
      console.error('发送自定义消息失败:', error);
      return {
        success: false,
        message: `发送自定义消息失败: ${error.message}`,
        error: error.message
      };
    }
  }

  /**
   * 获取通知类型列表
   */
  getNotificationTypes() {
    return Object.entries(this.notificationTypes).map(([key, name]) => ({
      type: key,
      name: name,
      category: this.getTypeCategory(key)
    }));
  }

  /**
   * 获取通知类型的分类
   */
  getTypeCategory(notificationType) {
    if (['daily_report', 'weekly_report', 'monthly_report'].includes(notificationType)) {
      return 'report';
    } else if (['maintenance_notification', 'error_alert'].includes(notificationType)) {
      return 'system';
    } else {
      return 'business';
    }
  }

  /**
   * 批量配置Webhook地址
   */
  async configureWebhook(webhookUrl, applyToTypes = ['all']) {
    try {
      // 确定要更新的通知类型
      let typesToUpdate = [];

      if (applyToTypes.includes('all')) {
        // 获取所有通知类型
        typesToUpdate = Object.keys(this.notificationTypes);
      } else {
        if (applyToTypes.includes('business')) {
          typesToUpdate.push(...['exchange_notification', 'stock_alert', 'new_user_welcome', 'new_product_notification']);
        }
        if (applyToTypes.includes('system')) {
          typesToUpdate.push(...['order_alert', 'maintenance_notification', 'error_alert']);
        }
        if (applyToTypes.includes('report')) {
          typesToUpdate.push(...['daily_report', 'weekly_report', 'monthly_report']);
        }
      }

      // 批量更新配置
      const updatePromises = typesToUpdate.map(async (notificationType) => {
        try {
          // 先检查配置是否存在，不存在则创建
          let config = await NotificationConfig.findOne({
            where: { notificationType }
          });

          if (!config) {
            // 创建新配置
            config = await NotificationConfig.create({
              notificationType,
              enabled: true,
              webhookUrl,
              retryCount: 3
            });
            console.log(`创建新的通知配置: ${notificationType}`);
          } else {
            // 更新现有配置
            await config.update({ webhookUrl });
            console.log(`更新通知配置: ${notificationType}`);
          }

          return {
            notificationType,
            success: true,
            typeName: this.notificationTypes[notificationType] || notificationType
          };
        } catch (error) {
          console.error(`更新通知配置失败 ${notificationType}:`, error);
          return {
            notificationType,
            success: false,
            error: error.message,
            typeName: this.notificationTypes[notificationType] || notificationType
          };
        }
      });

      const results = await Promise.all(updatePromises);

      const successCount = results.filter(r => r.success).length;
      const failedCount = results.filter(r => !r.success).length;

      return {
        totalUpdated: typesToUpdate.length,
        successCount,
        failedCount,
        results,
        webhookUrl,
        applyToTypes
      };
    } catch (error) {
      console.error('批量配置Webhook失败:', error);
      throw new Error('批量配置Webhook失败: ' + error.message);
    }
  }
}

module.exports = new NotificationConfigService();