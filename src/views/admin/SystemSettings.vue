<template>
  <div class="system-settings">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <h2>系统设置</h2>
          <p class="subtitle">管理系统全局配置和维护操作</p>
        </div>
      </template>
      
      <!-- 系统维护模块 -->
      <el-collapse accordion>
        <!-- 支付设置模块 -->
        <el-collapse-item name="payment">
          <template #title>
            <div class="module-title">
              <el-icon><money /></el-icon>
              <span>支付设置</span>
            </div>
          </template>
          
          <el-card class="inner-card" shadow="never">
            <div class="card-content">
              <h3>支付宝收款码</h3>
              <p class="description-text">
                管理系统使用的支付宝收款码，用户支付时将显示此收款码进行扫码支付。
              </p>
              
              <div class="qrcode-management">
                <!-- 当前收款码预览 -->
                <div class="current-qrcode" v-loading="qrcodeLoading">
                  <h4>当前收款码</h4>
                  <div v-if="paymentQRCode" class="qrcode-preview">
                    <el-image 
                      :src="paymentQRCode.qrcodeUrl" 
                      :preview-src-list="[paymentQRCode.qrcodeUrl]"
                      fit="contain"
                      class="qrcode-image"
                    >
                      <template #error>
                        <div class="image-placeholder">
                          <el-icon><picture-filled /></el-icon>
                          <span>暂无收款码</span>
                        </div>
                      </template>
                    </el-image>
                    <div class="qrcode-info">
                      <p v-if="paymentQRCode.uploadTime">
                        <strong>上传时间：</strong> {{ formatDate(paymentQRCode.uploadTime) }}
                      </p>
                      <el-button 
                        type="danger" 
                        size="small" 
                        @click="handleDeleteQRCode" 
                        :loading="deleteLoading"
                      >
                        删除收款码
                      </el-button>
                    </div>
                  </div>
                  <div v-else class="no-qrcode">
                    <div class="image-placeholder">
                      <el-icon><picture-filled /></el-icon>
                      <span>暂无收款码</span>
                    </div>
                    <p class="notice-text">请上传支付宝收款码图片</p>
                  </div>
                </div>
                
                <!-- 上传新收款码 -->
                <el-divider />
                <div class="upload-qrcode">
                  <h4>上传新收款码</h4>
                  <el-upload
                    ref="uploadRef"
                    action=""
                    :http-request="customUploadRequest"
                    :show-file-list="false"
                    :before-upload="beforeQRCodeUpload"
                    accept="image/*"
                    class="qrcode-uploader"
                  >
                    <el-button :loading="uploadLoading">
                      <el-icon><upload /></el-icon>
                      选择图片并上传
                    </el-button>
                    <template #tip>
                      <div class="el-upload__tip">
                        请上传清晰的支付宝收款码图片，仅支持 JPG/PNG 格式，大小不超过 2MB
                      </div>
                    </template>
                  </el-upload>
                </div>
              </div>
            </div>
          </el-card>
        </el-collapse-item>
      
        <el-collapse-item name="system-reset">
          <template #title>
            <div class="module-title">
              <el-icon><setting /></el-icon>
              <span>系统维护</span>
            </div>
          </template>
          
          <el-card class="inner-card" shadow="never">
            <div class="card-content">
              <h3>系统重置</h3>
              <p class="warning-text">
                警告：此操作将删除除管理员以外的所有用户、所有订单记录、所有公告和所有日志。
                商品和分类将被保留，所有商品库存将重置为50，累计兑换数量将重置为0。此操作不可逆，请谨慎操作。
              </p>
              
              <div class="actions">
                <el-button 
                  type="danger" 
                  :loading="resetLoading" 
                  @click="showResetConfirmation"
                >
                  系统重置
                </el-button>
              </div>
            </div>
          </el-card>
        </el-collapse-item>
        
        <!-- 职场管理模块 -->
        <el-collapse-item name="workplace-management">
          <template #title>
            <div class="module-title">
              <el-icon><location /></el-icon>
              <span>职场位置管理</span>
            </div>
          </template>
          
          <el-card class="inner-card" shadow="never">
            <div class="card-content">
              <h3>职场位置管理</h3>
              <p class="description-text">
                管理系统中的职场位置，用于用户注册和订单管理。统一管理职场位置可确保系统中职场数据的一致性。
              </p>
              
              <workplace-management />
            </div>
          </el-card>
        </el-collapse-item>

        <!-- 飞书群消息管理模块 -->
        <el-collapse-item name="feishu-notification">
          <template #title>
            <div class="module-title">
              <el-icon><chat-dot-round /></el-icon>
              <span>飞书群消息管理</span>
            </div>
          </template>
          
          <el-card class="inner-card" shadow="never">
            <div class="card-content">
              <h3>飞书群消息管理</h3>
              <p class="description-text">
                管理飞书群机器人通知功能，包括通知开关配置、手动发送测试和连接测试等功能。
              </p>
              
              <!-- 基础配置测试 -->
              <div class="section-container">
                <h4>基础配置与连接测试</h4>
                <div class="basic-config">
                  <div class="config-info">
                    <span class="config-label">Webhook地址状态:</span>
                    <el-tag :type="webhookConfigured ? 'success' : 'danger'">
                      {{ webhookConfigured ? '已配置' : '未配置' }}
                    </el-tag>
                  </div>
                  <div class="config-actions">
                    <el-button
                      @click="showWebhookConfigDialog"
                      :loading="configuringWebhook"
                      type="success"
                      v-if="!webhookConfigured"
                    >
                      <el-icon><setting /></el-icon>
                      配置Webhook
                    </el-button>
                    <el-button
                      @click="showWebhookConfigDialog"
                      :loading="configuringWebhook"
                      type="warning"
                      v-else
                    >
                      <el-icon><edit /></el-icon>
                      修改配置
                    </el-button>
                    <el-button
                      @click="testWebhookConnection"
                      :loading="testingConnection"
                      type="primary"
                    >
                      <el-icon><link /></el-icon>
                      测试连接
                    </el-button>
                    <el-button
                      @click="loadNotificationConfigs"
                      :loading="configsLoading"
                      type="info"
                    >
                      <el-icon><refresh /></el-icon>
                      刷新状态
                    </el-button>
                  </div>
                </div>
              </div>

              <!-- 通知配置开关 -->
              <div class="section-container">
                <h4>通知功能开关</h4>
                <div v-if="!webhookConfigured" class="webhook-warning">
                  <el-alert
                    title="Webhook未配置"
                    type="warning"
                    :closable="false"
                    show-icon
                    description="请先配置Webhook地址并测试连接成功后，才能使用通知功能。"
                  />
                </div>
                <el-loading-container v-loading="configsLoading">
                  <div class="notification-configs">
                    <div class="config-categories">
                      <!-- 业务通知 -->
                      <div class="config-category">
                        <h5>💼 业务通知</h5>
                        <div class="config-items">
                          <div 
                            v-for="config in businessConfigs" 
                            :key="config.notificationType" 
                            class="config-item"
                          >
                            <div class="config-info">
                              <span class="config-name">{{ config.typeName }}</span>
                              <span class="config-type">{{ config.notificationType }}</span>
                            </div>
                            <el-switch
                              v-model="config.enabled"
                              @change="updateNotificationConfig(config)"
                              :disabled="!webhookConfigured || updatingConfigs.includes(config.notificationType)"
                              active-color="#13ce66"
                              inactive-color="#ff4949"
                            />
                          </div>
                          <div v-if="businessConfigs.length === 0" class="empty-configs">
                            <span>暂无业务通知配置</span>
                          </div>
                        </div>
                      </div>

                      <!-- 定时报告 -->
                      <div class="config-category">
                        <h5>📊 定时报告</h5>
                        <div class="config-items">
                          <div 
                            v-for="config in reportConfigs" 
                            :key="config.notificationType" 
                            class="config-item"
                          >
                            <div class="config-info">
                              <span class="config-name">{{ config.typeName }}</span>
                              <span class="config-type">{{ config.notificationType }}</span>
                              <span v-if="config.scheduleTime" class="config-schedule">
                                定时: {{ config.scheduleTime }}
                              </span>
                            </div>
                            <el-switch
                              v-model="config.enabled"
                              @change="updateNotificationConfig(config)"
                              :disabled="!webhookConfigured || updatingConfigs.includes(config.notificationType)"
                              active-color="#13ce66"
                              inactive-color="#ff4949"
                            />
                          </div>
                          <div v-if="reportConfigs.length === 0" class="empty-configs">
                            <span>暂无定时报告配置</span>
                          </div>
                        </div>
                      </div>

                      <!-- 系统通知 -->
                      <div class="config-category">
                        <h5>⚙️ 系统通知</h5>
                        <div class="config-items">
                          <div 
                            v-for="config in systemConfigs" 
                            :key="config.notificationType" 
                            class="config-item"
                          >
                            <div class="config-info">
                              <span class="config-name">{{ config.typeName }}</span>
                              <span class="config-type">{{ config.notificationType }}</span>
                            </div>
                            <el-switch
                              v-model="config.enabled"
                              @change="updateNotificationConfig(config)"
                              :disabled="!webhookConfigured || updatingConfigs.includes(config.notificationType)"
                              active-color="#13ce66"
                              inactive-color="#ff4949"
                            />
                          </div>
                          <div v-if="systemConfigs.length === 0" class="empty-configs">
                            <span>暂无系统通知配置</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-loading-container>
              </div>

              <!-- 手动发送测试 -->
              <div class="section-container">
                <h4>手动发送测试</h4>
                <div class="manual-send">
                  <div class="send-categories">
                    <!-- 报告类测试 -->
                    <div class="send-category">
                      <h5>📊 数据报告测试</h5>
                      <div class="send-buttons">
                        <el-button 
                          v-for="reportType in reportTypes" 
                          :key="reportType.type"
                          @click="sendTestNotification(reportType.type)"
                          :loading="sendingTests.includes(reportType.type)"
                          :disabled="!isConfigEnabled(reportType.type)"
                          size="small"
                        >
                          {{ reportType.name }}
                        </el-button>
                      </div>
                    </div>

                    <!-- 业务通知测试 -->
                    <div class="send-category">
                      <h5>💼 业务通知测试</h5>
                      <div class="send-buttons">
                        <el-button 
                          v-for="businessType in businessTypes" 
                          :key="businessType.type"
                          @click="sendTestNotification(businessType.type)"
                          :loading="sendingTests.includes(businessType.type)"
                          :disabled="!isConfigEnabled(businessType.type)"
                          size="small"
                        >
                          {{ businessType.name }}
                        </el-button>
                      </div>
                    </div>

                    <!-- 系统通知测试 -->
                    <div class="send-category">
                      <h5>⚙️ 系统通知测试</h5>
                      <div class="send-buttons">
                        <el-button 
                          v-for="systemType in systemTypes" 
                          :key="systemType.type"
                          @click="sendTestNotification(systemType.type)"
                          :loading="sendingTests.includes(systemType.type)"
                          :disabled="!isConfigEnabled(systemType.type)"
                          size="small"
                        >
                          {{ systemType.name }}
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 自定义消息发送 -->
              <div class="section-container">
                <h4>自定义消息发送</h4>
                <div class="custom-message">
                  <el-input
                    v-model="customMessage"
                    type="textarea"
                    :rows="4"
                    placeholder="输入要发送的自定义消息内容..."
                    maxlength="1000"
                    show-word-limit
                  />
                  <div class="custom-message-actions">
                    <el-button 
                      type="primary" 
                      @click="sendCustomMessage"
                      :loading="sendingCustomMessage"
                      :disabled="!customMessage.trim()"
                    >
                      <el-icon><promotion /></el-icon>
                      发送自定义消息
                    </el-button>
                  </div>
                </div>
              </div>

              <!-- 通知监控和统计 -->
              <div class="section-container">
                <h4>通知监控和统计</h4>
                
                <!-- 统计数据卡片 -->
                <div class="stats-cards" v-loading="statsLoading">
                  <div class="stats-card">
                    <div class="stats-icon">📊</div>
                    <div class="stats-content">
                      <div class="stats-number">{{ notificationStats.totalStats.total }}</div>
                      <div class="stats-label">总发送量</div>
                    </div>
                  </div>
                  <div class="stats-card success">
                    <div class="stats-icon">✅</div>
                    <div class="stats-content">
                      <div class="stats-number">{{ notificationStats.totalStats.success }}</div>
                      <div class="stats-label">发送成功</div>
                    </div>
                  </div>
                  <div class="stats-card failed">
                    <div class="stats-icon">❌</div>
                    <div class="stats-content">
                      <div class="stats-number">{{ notificationStats.totalStats.failed }}</div>
                      <div class="stats-label">发送失败</div>
                    </div>
                  </div>
                  <div class="stats-card rate">
                    <div class="stats-icon">📈</div>
                    <div class="stats-content">
                      <div class="stats-number">{{ notificationStats.totalStats.successRate }}%</div>
                      <div class="stats-label">成功率</div>
                    </div>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div class="monitor-actions">
                  <el-button @click="loadNotificationStats" :loading="statsLoading">
                    <el-icon><refresh /></el-icon>
                    刷新统计
                  </el-button>
                  <el-button @click="processFailedRetries" :loading="processingRetries">
                    <el-icon><refresh /></el-icon>
                    处理失败重试
                  </el-button>
                  <el-button @click="showHistoryDialog = true">
                    <el-icon><document /></el-icon>
                    查看发送历史
                  </el-button>
                </div>
              </div>
            </div>
          </el-card>
        </el-collapse-item>
        
        <!-- 飞书群高级管理功能 -->
        <el-collapse-item name="feishu-advanced">
          <template #title>
            <div class="module-title">
              <el-icon><ChatDotRound /></el-icon>
              <span>飞书群高级管理</span>
            </div>
          </template>
          
          <el-card class="inner-card" shadow="never">
            <div class="card-content">
              <h3>群管理高级功能</h3>
              <p class="description-text">
                飞书群管理高级功能，包括自定义消息模板、智能发送时间控制和高级诊断工具。
              </p>
              
              <div class="advanced-features">
                <el-row :gutter="20">
                  <el-col :span="8">
                    <router-link to="/admin/system/message-templates">
                      <el-card class="feature-card" shadow="hover">
                        <template #header>
                          <div class="feature-header">
                            <el-icon><Document /></el-icon>
                            <span>自定义消息模板</span>
                          </div>
                        </template>
                        <div class="feature-description">
                          创建和管理飞书消息模板，支持变量替换和多种消息类型，提高消息发送效率。
                        </div>
                      </el-card>
                    </router-link>
                  </el-col>
                  
                  <el-col :span="8">
                    <router-link to="/admin/system/intelligent-schedule">
                      <el-card class="feature-card" shadow="hover">
                        <template #header>
                          <div class="feature-header">
                            <el-icon><Setting /></el-icon>
                            <span>智能发送时间控制</span>
                          </div>
                        </template>
                        <div class="feature-description">
                          配置智能发送时间调度，支持固定时间、智能调度和条件触发，优化用户接收体验。
                        </div>
                      </el-card>
                    </router-link>
                  </el-col>
                  
                  <el-col :span="8">
                    <router-link to="/admin/system/diagnostic-tools">
                      <el-card class="feature-card" shadow="hover">
                        <template #header>
                          <div class="feature-header">
                            <el-icon><Promotion /></el-icon>
                            <span>高级诊断工具</span>
                          </div>
                        </template>
                        <div class="feature-description">
                          提供系统健康检查、Webhook连接测试和消息发送分析等高级诊断功能，确保通知发送的可靠性。
                        </div>
                      </el-card>
                    </router-link>
                  </el-col>
                </el-row>
              </div>
            </div>
          </el-card>
        </el-collapse-item>
      </el-collapse>
    </el-card>
    
    <!-- 系统重置确认对话框 -->
    <el-dialog
      title="系统重置确认"
      v-model="resetDialogVisible"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="reset-confirmation">
        <el-alert
          title="危险操作！此操作将不可逆地重置系统数据。"
          type="error"
          :closable="false"
          show-icon
          description="执行此操作前，请确保已做好数据备份。"
        />
        
        <div class="confirmation-details">
          <h4>将被删除的内容：</h4>
          <ul>
            <li>所有非管理员用户账户</li>
            <li>所有兑换订单记录</li>
            <li>所有系统公告</li>
            <li>所有系统日志</li>
            <li>所有用户反馈</li>
            <li>所有系统通知</li>
          </ul>
          
          <h4>将被保留的内容：</h4>
          <ul>
            <li>管理员账户</li>
            <li>所有商品信息（库存将重置为50，累计兑换将重置为0）</li>
            <li>所有分类信息</li>
          </ul>
        </div>
        
        <div class="confirmation-input">
          <p>请输入 "RESET" 以确认操作：</p>
          <el-input 
            v-model="confirmationText" 
            placeholder="请输入 RESET" 
          />
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="resetDialogVisible = false">取消</el-button>
          <el-button
            type="danger"
            :loading="resetLoading"
            :disabled="confirmationText !== 'RESET'"
            @click="executeReset"
          >
            确认重置
          </el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 重置结果对话框 -->
    <el-dialog
      title="系统重置结果"
      v-model="resultDialogVisible"
      width="600px"
    >
      <div class="reset-result" v-if="resetResult">
        <el-alert
          :title="resetResult.success ? '系统重置成功' : '系统重置失败'"
          :type="resetResult.success ? 'success' : 'error'"
          :closable="false"
          show-icon
        />
        
        <div class="result-stats" v-if="resetResult.success && resetResult.stats">
          <h4>数据清理统计：</h4>
          <el-table :data="getStatsData()" stripe>
            <el-table-column prop="name" label="数据类型" width="180" />
            <el-table-column label="清理数量">
              <template #default="scope">
                <span v-if="typeof scope.row.count === 'number'">{{ scope.row.count }}</span>
                <span v-else>{{ Number(scope.row.count) || 0 }}</span>
              </template>
            </el-table-column>
          </el-table>
          
          <h4 class="mt-20">商品库存统计：</h4>
          <p>已将 {{ resetResult.stats?.updated?.products || 0 }} 个商品的库存统一设置为50，累计兑换数量重置为0</p>
        </div>
        
        <div v-if="!resetResult.success" class="error-message">
          <p>错误信息: {{ resetResult.error }}</p>
          <p>请联系系统管理员处理。</p>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="resultDialogVisible = false">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 发送历史对话框 -->
    <el-dialog
      title="通知发送历史"
      v-model="showHistoryDialog"
      width="90%"
      top="5vh"
    >
      <div class="history-container">
        <!-- 搜索筛选 -->
        <div class="history-filters">
          <el-row :gutter="16">
            <el-col :span="6">
              <el-select 
                v-model="historyFilters.notificationType" 
                placeholder="通知类型"
                clearable
                @change="loadNotificationHistory"
              >
                <el-option label="全部类型" value="" />
                <el-option 
                  v-for="type in allNotificationTypes" 
                  :key="type.type"
                  :label="type.name" 
                  :value="type.type" 
                />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-select 
                v-model="historyFilters.status" 
                placeholder="发送状态"
                clearable
                @change="loadNotificationHistory"
              >
                <el-option label="全部状态" value="" />
                <el-option label="发送成功" value="success" />
                <el-option label="发送失败" value="failed" />
                <el-option label="等待重试" value="pending" />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-select 
                v-model="historyFilters.triggerSource" 
                placeholder="触发源"
                clearable
                @change="loadNotificationHistory"
              >
                <el-option label="全部来源" value="" />
                <el-option label="自动触发" value="auto" />
                <el-option label="手动发送" value="manual" />
                <el-option label="测试发送" value="test" />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-button @click="loadNotificationHistory" :loading="historyLoading">
                <el-icon><search /></el-icon>
                查询
              </el-button>
            </el-col>
          </el-row>
        </div>

        <!-- 历史列表 -->
        <el-table 
          :data="notificationHistory.logs" 
          v-loading="historyLoading"
          height="400"
        >
          <el-table-column prop="id" label="ID" width="60" />
          <el-table-column prop="notificationType" label="通知类型" width="140">
            <template #default="scope">
              <el-tag size="small">{{ getNotificationTypeName(scope.row.notificationType) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag 
                :type="getStatusTagType(scope.row.status)"
                size="small"
              >
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="triggerSource" label="触发源" width="80">
            <template #default="scope">
              <el-tag 
                :type="getTriggerTagType(scope.row.triggerSource)"
                size="small"
              >
                {{ getTriggerText(scope.row.triggerSource) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="retryCount" label="重试次数" width="80" />
          <el-table-column prop="responseTime" label="响应时间" width="90">
            <template #default="scope">
              <span v-if="scope.row.responseTime">{{ scope.row.responseTime }}ms</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="errorMessage" label="错误信息" min-width="150">
            <template #default="scope">
              <span v-if="scope.row.errorMessage" class="error-text">
                {{ scope.row.errorMessage }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="createdAt" label="发送时间" width="160">
            <template #default="scope">
              {{ formatDate(scope.row.createdAt) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="scope">
              <el-button 
                v-if="scope.row.status === 'failed'"
                @click="retryNotification(scope.row.id)"
                :loading="retryingNotifications.includes(scope.row.id)"
                type="primary"
                size="small"
              >
                重试
              </el-button>
              <span v-else>-</span>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="history-pagination">
          <el-pagination
            v-model:current-page="historyFilters.page"
            v-model:page-size="historyFilters.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="notificationHistory.pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadNotificationHistory"
            @current-change="loadNotificationHistory"
          />
        </div>
      </div>
    </el-dialog>

    <!-- Webhook配置对话框 -->
    <el-dialog
      v-model="webhookConfigDialogVisible"
      title="Webhook地址配置"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="webhook-config-content">
        <el-alert
          title="配置说明"
          type="info"
          :closable="false"
          show-icon
        >
          <p>配置飞书群机器人的Webhook地址，用于发送各种通知消息。</p>
          <p>获取方式：在飞书群中添加机器人，复制Webhook地址。</p>
        </el-alert>

        <el-form
          ref="webhookFormRef"
          :model="webhookForm"
          :rules="webhookRules"
          label-width="120px"
          style="margin-top: 20px;"
        >
          <el-form-item label="Webhook地址" prop="webhookUrl">
            <el-input
              v-model="webhookForm.webhookUrl"
              placeholder="请输入飞书群机器人Webhook地址"
              type="textarea"
              :rows="3"
              :disabled="configuringWebhook"
            />
          </el-form-item>

          <el-form-item label="应用范围">
            <el-checkbox-group v-model="webhookForm.applyToTypes">
              <el-checkbox value="all">应用到所有通知类型</el-checkbox>
              <el-checkbox value="business">仅业务通知</el-checkbox>
              <el-checkbox value="report">仅报告通知</el-checkbox>
              <el-checkbox value="system">仅系统通知</el-checkbox>
            </el-checkbox-group>
            <div style="margin-top: 8px; font-size: 12px; color: #909399;">
              业务通知：兑换申请、库存告警、新用户欢迎、新品上架<br>
              报告通知：每日报告、每周报告、月度报告<br>
              系统通知：订单预警、维护通知、错误告警
            </div>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="webhookConfigDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="saveWebhookConfig"
            :loading="configuringWebhook"
          >
            保存配置
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, onMounted, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { resetSystem, getPaymentQRCode, uploadPaymentQRCode, deletePaymentQRCode } from '../../api/system';
import { PictureFilled, Upload, Setting, Money, Location, ChatDotRound, Link, Promotion, Refresh, Document, Search, Edit } from '@element-plus/icons-vue';
import WorkplaceManagement from './system/WorkplaceManagement.vue';
import api from '@/api';

export default {
  name: 'SystemSettings',
  components: {
    WorkplaceManagement,
    PictureFilled,
    Upload,
    Setting,
    Money,
    Location,
    ChatDotRound,
    Link,
    Promotion,
    Refresh,
    Document,
    Search,
    Edit
  },
  
  setup() {
    // 重置相关状态
    const resetLoading = ref(false);
    const resetDialogVisible = ref(false);
    const resultDialogVisible = ref(false);
    const confirmationText = ref('');
    const resetResult = ref(null);
    
    // 支付码相关状态
    const paymentQRCode = ref(null);
    const qrcodeLoading = ref(false);
    const uploadLoading = ref(false);
    const deleteLoading = ref(false);
    const uploadRef = ref(null);
    
    // 飞书群消息管理相关状态
    const webhookConfigured = ref(false);
    const testingConnection = ref(false);
    const configsLoading = ref(false);
    const sendingCustomMessage = ref(false);
    const notificationConfigs = ref([]);
    const updatingConfigs = ref([]);
    const sendingTests = ref([]);
    const customMessage = ref('');

    // Webhook配置相关状态
    const webhookConfigDialogVisible = ref(false);
    const configuringWebhook = ref(false);
    const webhookFormRef = ref(null);
    const webhookForm = ref({
      webhookUrl: '',
      applyToTypes: ['all']
    });
    const webhookRules = ref({
      webhookUrl: [
        { required: true, message: '请输入Webhook地址', trigger: 'blur' },
        {
          pattern: /^https:\/\/open\.feishu\.cn\/open-apis\/bot\/v2\/hook\/[a-zA-Z0-9\-_]+$/,
          message: '请输入有效的飞书机器人Webhook地址',
          trigger: 'blur'
        },
        {
          min: 10,
          message: 'Webhook地址长度不能少于10个字符',
          trigger: 'blur'
        }
      ]
    });
    
    // 通知类型分类
    const reportTypes = ref([
      { type: 'daily_report', name: '每日报告' },
      { type: 'weekly_report', name: '每周报告' },
      { type: 'monthly_report', name: '月度报告' }
    ]);
    const businessTypes = ref([
      { type: 'exchange_notification', name: '兑换申请' },
      { type: 'stock_alert', name: '库存告警' },
      { type: 'new_user_welcome', name: '新用户欢迎' },
      { type: 'new_product_notification', name: '新品上架' }
    ]);
    const systemTypes = ref([
      { type: 'order_alert', name: '订单预警' },
      { type: 'maintenance_notification', name: '维护通知' },
      { type: 'error_alert', name: '错误告警' }
    ]);

    // 分类配置的计算属性
    const businessConfigs = computed(() => {
      const defaultConfigs = businessTypes.value.map(type => ({
        notificationType: type.type,
        typeName: type.name,
        enabled: true,
        webhookUrl: null
      }));
      
      // 如果通知配置数组为空或没有业务通知配置项，返回默认配置
      if (notificationConfigs.value.length === 0 || 
          !notificationConfigs.value.some(c => ['exchange_notification', 'stock_alert', 'new_user_welcome', 'new_product_notification'].includes(c.notificationType))) {
        console.log('⚠️ 未找到业务通知配置，使用默认配置项');
        return defaultConfigs;
      }
      
      // 使用存在的配置，对于不存在的使用默认配置
      return businessTypes.value.map(type => {
        const existingConfig = notificationConfigs.value.find(c => c.notificationType === type.type);
        if (existingConfig) {
          return existingConfig;
        } else {
          console.log(`⚠️ 未找到配置项: ${type.type}，使用默认配置`);
          return {
            notificationType: type.type,
            typeName: type.name,
            enabled: true,
            webhookUrl: null
          };
        }
      });
    });

    const reportConfigs = computed(() => {
      const defaultConfigs = reportTypes.value.map(type => ({
        notificationType: type.type,
        typeName: type.name,
        enabled: true,
        webhookUrl: null
      }));
      
      // 如果通知配置数组为空或没有报告通知配置项，返回默认配置
      if (notificationConfigs.value.length === 0 ||
          !notificationConfigs.value.some(c => ['daily_report', 'weekly_report', 'monthly_report'].includes(c.notificationType))) {
        console.log('⚠️ 未找到报告通知配置，使用默认配置项');
        return defaultConfigs;
      }
      
      // 使用存在的配置，对于不存在的使用默认配置
      return reportTypes.value.map(type => {
        const existingConfig = notificationConfigs.value.find(c => c.notificationType === type.type);
        if (existingConfig) {
          return existingConfig;
        } else {
          console.log(`⚠️ 未找到配置项: ${type.type}，使用默认配置`);
          return {
            notificationType: type.type,
            typeName: type.name,
            enabled: true,
            webhookUrl: null
          };
        }
      });
    });

    const systemConfigs = computed(() => {
      const defaultConfigs = systemTypes.value.map(type => ({
        notificationType: type.type,
        typeName: type.name,
        enabled: true,
        webhookUrl: null
      }));
      
      // 如果通知配置数组为空或没有系统通知配置项，返回默认配置
      if (notificationConfigs.value.length === 0 ||
          !notificationConfigs.value.some(c => ['order_alert', 'maintenance_notification', 'error_alert'].includes(c.notificationType))) {
        console.log('⚠️ 未找到系统通知配置，使用默认配置项');
        return defaultConfigs;
      }
      
      // 使用存在的配置，对于不存在的使用默认配置
      return systemTypes.value.map(type => {
        const existingConfig = notificationConfigs.value.find(c => c.notificationType === type.type);
        if (existingConfig) {
          return existingConfig;
        } else {
          console.log(`⚠️ 未找到配置项: ${type.type}，使用默认配置`);
          return {
            notificationType: type.type,
            typeName: type.name,
            enabled: true,
            webhookUrl: null
          };
        }
      });
    });

    // 加载通知配置
    const loadNotificationConfigs = async () => {
      try {
        configsLoading.value = true;
        console.log('🔧🔧🔧 [DEBUG] 开始加载通知配置... 🔧🔧🔧');
        console.log('🔧🔧🔧 [DEBUG] 当前webhookConfigured状态:', webhookConfigured.value);

        const response = await api.get('/system/notification-configs');
        console.log('🔧 API响应:', response.data);

        if (response.data.success) {
          notificationConfigs.value = response.data.data;
          console.log('🔧 设置通知配置数据:', notificationConfigs.value);

          // 检查webhook是否配置 - 修复状态检测逻辑
          console.log('🔧 开始检测Webhook配置状态...');
          console.log('🔧 获取到的通知配置数据:', notificationConfigs.value);

          // 1. 检查数据库配置项中是否有有效的webhookUrl
          const configsWithWebhook = notificationConfigs.value.filter(config => {
            const hasValidWebhook = config.webhookUrl &&
              config.webhookUrl.trim() !== '' &&
              config.webhookUrl !== 'null' &&
              config.webhookUrl.startsWith('https://open.feishu.cn/');

            console.log(`🔧 检查配置 ${config.notificationType}:`, {
              webhookUrl: config.webhookUrl,
              hasValidWebhook
            });

            return hasValidWebhook;
          });

          console.log('🔧 数据库中有效的Webhook配置数量:', configsWithWebhook.length);
          console.log('🔧 有效的Webhook配置:', configsWithWebhook.map(c => ({
            type: c.notificationType,
            url: c.webhookUrl
          })));

          if (configsWithWebhook.length > 0) {
            console.log('🔧 设置webhookConfigured为true...');
            webhookConfigured.value = true;
            console.log('✅ 检测到数据库中有有效的Webhook地址:', configsWithWebhook[0].webhookUrl);
            console.log('🔧 当前webhookConfigured状态:', webhookConfigured.value);
          } else {
            // 2. 如果数据库中没有有效配置，测试环境变量中的webhook
            console.log('⚠️ 数据库中未找到有效Webhook地址，测试环境变量配置...');
            try {
              const testResponse = await api.post('/system/test-webhook');
              console.log('🔧 环境变量Webhook测试响应:', testResponse.data);

              if (testResponse.data.success && testResponse.data.data.success) {
                console.log('🔧 设置webhookConfigured为true（环境变量）...');
                webhookConfigured.value = true;
                console.log('✅ 环境变量中的Webhook地址测试成功');
              } else {
                webhookConfigured.value = false;
                console.log('❌ 环境变量中的Webhook地址测试失败:', testResponse.data.data?.error);
              }
            } catch (testError) {
              webhookConfigured.value = false;
              console.log('❌ 环境变量webhook测试请求失败:', testError.response?.data?.message || testError.message);
            }
          }

          console.log('🔧 最终Webhook配置状态:', webhookConfigured.value ? '已配置' : '未配置');
          console.log('🔧 webhookConfigured.value =', webhookConfigured.value);
        } else {
          console.log('❌ API响应失败:', response.data);
        }
      } catch (error) {
        console.error('🔧 加载通知配置出错:', error);
        ElMessage.error('加载通知配置失败: ' + (error.response?.data?.message || error.message));
      } finally {
        configsLoading.value = false;
        console.log('🔧 loadNotificationConfigs完成，最终状态:', webhookConfigured.value);
        
        // 在加载完成后再次检查配置状态并诊断
        if (notificationConfigs.value.length > 0) {
          console.log('🔍 配置加载完成，再次诊断状态:');
          console.log('🔍 webhookConfigured =', webhookConfigured.value);
          console.log('🔍 notificationConfigs.length =', notificationConfigs.value.length);
          
          // 如果有配置但webhookConfigured为false，尝试检查环境变量
          if (!webhookConfigured.value) {
            console.log('⚠️ 发现数据异常：有配置项但webhookConfigured为false，尝试修正...');
            const anyConfigWithWebhook = notificationConfigs.value.some(config => 
              config.webhookUrl && 
              config.webhookUrl.trim() !== '' && 
              config.webhookUrl !== 'null' &&
              config.webhookUrl.startsWith('https://open.feishu.cn/')
            );
            
            if (anyConfigWithWebhook) {
              console.log('✅ 发现有效的webhook配置，强制设置webhookConfigured为true');
              webhookConfigured.value = true;
            }
          }
        }
      }
    };

    // 更新通知配置
    const updateNotificationConfig = async (config) => {
      try {
        console.log('🔧🔧🔧 [DEBUG] 开始更新通知配置:', config.notificationType, '启用状态:', config.enabled);
        console.log('🔧🔧🔧 [DEBUG] 配置详情:', {
          id: config.id,
          enabled: config.enabled,
          webhookUrl: config.webhookUrl ? `${config.webhookUrl.substring(0, 20)}...` : 'null',
          typeName: config.typeName
        });
        console.log('🔧🔧🔧 [DEBUG] webhookConfigured状态:', webhookConfigured.value);

        // 防止重复请求
        if (updatingConfigs.value.includes(config.notificationType)) {
          console.log('⚠️ 配置更新中，跳过重复请求');
          return;
        }

        updatingConfigs.value.push(config.notificationType);
        
        // 默认的Webhook URL - 使用固定的有效URL
        const DEFAULT_WEBHOOK_URL = "https://open.feishu.cn/open-apis/bot/v2/hook/e6cff700-4172-4039-a700-43c8f43765fc";
        
        // 准备更新数据
        let updateData = { 
          enabled: config.enabled,
          // 始终包含一个有效的webhookUrl
          webhookUrl: config.webhookUrl || DEFAULT_WEBHOOK_URL
        };
        
        console.log('🔧 发送更新请求:', {
          notificationType: config.notificationType,
          updateData: {
            ...updateData,
            webhookUrl: updateData.webhookUrl ? updateData.webhookUrl.substring(0, 20) + '...' : 'null'
          }
        });

        const response = await api.put(`/system/notification-configs/${config.notificationType}`, updateData);

        console.log('🔧🔧🔧 [DEBUG] 通知配置更新响应:', response.data);
        console.log('🔧🔧🔧 [DEBUG] 响应状态:', response.status);
        console.log('🔧🔧🔧 [DEBUG] 响应成功标志:', response.data.success);

        if (response.data.success) {
          ElMessage.success(`${config.typeName} ${config.enabled ? '已启用' : '已禁用'}`);
          console.log('✅ 通知配置更新成功');
          
          // 更新本地配置对象
          if (response.data.data) {
            const updatedConfig = response.data.data;
            console.log('✅ 更新前配置:', {
              ...config,
              webhookUrl: config.webhookUrl ? config.webhookUrl.substring(0, 20) + '...' : 'null'
            });
            
            // 更新对象属性
            Object.assign(config, updatedConfig);
            
            console.log('✅ 配置已更新:', {
              ...config,
              webhookUrl: config.webhookUrl ? config.webhookUrl.substring(0, 20) + '...' : 'null'
            });
            
            // 添加这个新配置到notificationConfigs中，如果还不存在
            const existingIndex = notificationConfigs.value.findIndex(c => 
              c.notificationType === config.notificationType
            );
            
            if (existingIndex === -1) {
              console.log('✅ 将新配置添加到notificationConfigs中');
              notificationConfigs.value.push(config);
            } else if (existingIndex >= 0) {
              console.log('✅ 更新notificationConfigs中的现有配置');
              notificationConfigs.value[existingIndex] = config;
            }
          }
          
          // 配置更新成功后设置webhookConfigured为true
          if (!webhookConfigured.value) {
            console.log('✅ 配置更新成功，设置webhookConfigured为true');
            webhookConfigured.value = true;
          }
          
        } else {
          // 回滚开关状态
          config.enabled = !config.enabled;
          ElMessage.error('更新配置失败: ' + response.data.message);
        }
      } catch (error) {
        console.error('🔧🔧🔧 [DEBUG] 更新通知配置错误:', error);
        console.error('🔧🔧🔧 [DEBUG] 错误类型:', error.constructor.name);
        console.error('🔧🔧🔧 [DEBUG] 错误消息:', error.message);

        // 回滚开关状态
        config.enabled = !config.enabled;
        console.log('🔧🔧🔧 [DEBUG] 回滚开关状态到:', config.enabled);

        if (error.response) {
          console.error('🔧🔧🔧 [DEBUG] 服务器响应错误:', error.response.data);
          console.error('🔧🔧🔧 [DEBUG] 响应状态码:', error.response.status);
          const errorMsg = error.response.data?.message || error.response.statusText || '服务器错误';
          ElMessage.error('更新配置失败: ' + errorMsg);
        } else {
          console.error('🔧🔧🔧 [DEBUG] 网络或其他错误:', error.message);
          ElMessage.error('更新配置失败: ' + (error.message || '网络错误'));
        }
      } finally {
        updatingConfigs.value = updatingConfigs.value.filter(type => type !== config.notificationType);
      }
    };

    // 测试webhook连接
    const testWebhookConnection = async () => {
      try {
        testingConnection.value = true;
        const response = await api.post('/system/test-webhook');
        
        if (response.data.success) {
          const { responseTime } = response.data.data;
          ElMessage.success(`连接测试成功！响应时间: ${responseTime}ms`);
          // 连接测试成功后直接设置webhookConfigured为true
          webhookConfigured.value = true;
          console.log("✅ Webhook连接测试成功，已更新状态为已配置");
          
          // 打印当前所有配置项的状态
          console.log("🔍 诊断当前通知配置状态:");
          console.log("🔍 webhookConfigured =", webhookConfigured.value);
          console.log("🔍 notificationConfigs =", notificationConfigs.value);
          
          // 检查所有测试按钮的状态
          console.log("🔍 测试按钮启用状态:");
          reportTypes.value.forEach(type => {
            console.log(`🔍 ${type.name}(${type.type}) 是否启用:`, isConfigEnabled(type.type));
          });
          businessTypes.value.forEach(type => {
            console.log(`🔍 ${type.name}(${type.type}) 是否启用:`, isConfigEnabled(type.type));
          });
          systemTypes.value.forEach(type => {
            console.log(`🔍 ${type.name}(${type.type}) 是否启用:`, isConfigEnabled(type.type));
          });
          
          // 如果通知配置为空，尝试重新加载配置
          if (notificationConfigs.value.length === 0) {
            console.log("⚠️ 通知配置为空，尝试重新加载配置");
            await loadNotificationConfigs();
          }
        } else {
          ElMessage.error('连接测试失败: ' + response.data.data.error);
        }
      } catch (error) {
        ElMessage.error('连接测试失败: ' + (error.response?.data?.message || error.message));
      } finally {
        testingConnection.value = false;
      }
    };

    // 发送测试通知
    const sendTestNotification = async (notificationType) => {
      try {
        sendingTests.value.push(notificationType);
        
        const response = await api.post(`/system/test-notification/${notificationType}`);
        
        if (response.data.success) {
          ElMessage.success(response.data.message);
        } else {
          ElMessage.error(response.data.message);
        }
      } catch (error) {
        ElMessage.error('发送测试通知失败: ' + (error.response?.data?.message || error.message));
      } finally {
        sendingTests.value = sendingTests.value.filter(type => type !== notificationType);
      }
    };

    // 发送自定义消息
    const sendCustomMessage = async () => {
      if (!customMessage.value.trim()) {
        ElMessage.warning('请输入消息内容');
        return;
      }

      try {
        sendingCustomMessage.value = true;
        
        const response = await api.post('/system/send-custom-message', {
          content: customMessage.value.trim()
        });
        
        if (response.data.success) {
          ElMessage.success('自定义消息发送成功');
          customMessage.value = '';
        } else {
          ElMessage.error(response.data.message);
        }
      } catch (error) {
        ElMessage.error('发送自定义消息失败: ' + (error.response?.data?.message || error.message));
      } finally {
        sendingCustomMessage.value = false;
      }
    };

    // 检查配置是否启用
    const isConfigEnabled = (notificationType) => {
      // 首先检查Webhook是否配置
      if (!webhookConfigured.value) {
        console.log(`🔧 ${notificationType} 禁用原因: Webhook未配置`);
        return false;
      }

      const config = notificationConfigs.value.find(c => c.notificationType === notificationType);
      if (!config) {
        console.log(`🔧 ${notificationType} 找不到配置，但Webhook已配置，允许使用`);
        // 如果Webhook已配置但找不到该类型的配置，仍然允许测试
        return true;
      }
      
      console.log(`🔧 ${notificationType} 启用状态:`, config.enabled);
      return config.enabled;
    };

    // 显示Webhook配置对话框
    const showWebhookConfigDialog = () => {
      // 获取当前配置的webhook地址
      const currentWebhook = notificationConfigs.value.find(config =>
        config.webhookUrl && config.webhookUrl.trim() !== ''
      );

      webhookForm.value = {
        webhookUrl: currentWebhook?.webhookUrl || '',
        applyToTypes: ['all']
      };

      webhookConfigDialogVisible.value = true;
    };

    // 保存Webhook配置
    const saveWebhookConfig = async () => {
      try {
        // 表单验证
        if (!webhookFormRef.value) {
          ElMessage.error('表单引用未找到');
          return;
        }

        await webhookFormRef.value.validate();

        configuringWebhook.value = true;

        const { webhookUrl, applyToTypes } = webhookForm.value;

        console.log('🔧 开始保存Webhook配置:', { webhookUrl, applyToTypes });

        // 使用新的批量配置API
        const response = await api.post('/system/configure-webhook', {
          webhookUrl: webhookUrl.trim(),
          applyToTypes
        });

        console.log('🔧 Webhook配置响应:', response.data);

        if (response.data.success) {
          const { successCount, failedCount, totalUpdated } = response.data.data;

          if (failedCount > 0) {
            ElMessage.warning(`Webhook配置部分成功：${successCount}/${totalUpdated} 个配置项更新成功`);
          } else {
            ElMessage.success(`Webhook配置保存成功：已更新 ${successCount} 个配置项`);
          }

          webhookConfigDialogVisible.value = false;

          // 立即更新状态为已配置
          webhookConfigured.value = true;
          console.log('✅ Webhook配置保存成功，状态已更新为已配置');

          // 重新加载配置以获取最新数据
          await loadNotificationConfigs();
        } else {
          ElMessage.error('保存Webhook配置失败: ' + response.data.message);
        }

      } catch (error) {
        console.error('🔧 保存Webhook配置错误:', error);

        if (error.fields) {
          // 表单验证错误
          ElMessage.error('请检查输入内容是否符合要求');
          console.log('表单验证错误:', error.fields);
        } else if (error.response) {
          // HTTP错误响应
          const errorMsg = error.response.data?.message || error.response.statusText || '服务器错误';
          ElMessage.error('保存Webhook配置失败: ' + errorMsg);
        } else {
          // 网络错误或其他错误
          ElMessage.error('保存Webhook配置失败: ' + (error.message || '未知错误'));
        }
      } finally {
        configuringWebhook.value = false;
      }
    };

    // 加载支付码信息
    const loadPaymentQRCode = async () => {
      try {
        qrcodeLoading.value = true;
        const response = await getPaymentQRCode();
        console.log('获取到的支付码信息:', response);
        paymentQRCode.value = response;
      } catch (error) {
        console.error('加载支付码失败:', error);
        ElMessage.error('获取支付码信息失败: ' + (error.message || '未知错误'));
      } finally {
        qrcodeLoading.value = false;
      }
    };
    
    // 上传前验证
    const beforeQRCodeUpload = (file) => {
      const isImage = file.type.startsWith('image/');
      const isLt2M = file.size / 1024 / 1024 < 2;
      
      if (!isImage) {
        ElMessage.error('只能上传图片文件!');
        return false;
      }
      
      if (!isLt2M) {
        ElMessage.error('图片大小不能超过 2MB!');
        return false;
      }
      
      return true;
    };
    
    // 自定义上传方法
    const customUploadRequest = async (options) => {
      try {
        uploadLoading.value = true;
        const { file } = options;
        
        const formData = new FormData();
        formData.append('file', file);
        
        const response = await uploadPaymentQRCode(formData);
        
        if (response && response.success) {
          ElMessage.success('支付码上传成功');
          // 重新加载支付码信息
          await loadPaymentQRCode();
        } else {
          ElMessage.error('支付码上传失败: ' + (response?.message || '未知错误'));
        }
      } catch (error) {
        console.error('上传支付码错误:', error);
        ElMessage.error('上传支付码失败: ' + (error.message || '未知错误'));
      } finally {
        uploadLoading.value = false;
      }
    };
    
    // 删除支付码
    const handleDeleteQRCode = async () => {
      try {
        await ElMessageBox.confirm(
          '确定要删除当前的支付宝收款码吗？删除后用户将无法看到收款码进行支付。',
          '删除确认',
          {
            confirmButtonText: '确认删除',
            cancelButtonText: '取消',
            type: 'warning'
          }
        );
        
        deleteLoading.value = true;
        const response = await deletePaymentQRCode();
        
        if (response && response.success) {
          ElMessage.success('支付码删除成功');
          paymentQRCode.value = null;
        } else {
          ElMessage.error('支付码删除失败: ' + (response?.message || '未知错误'));
        }
      } catch (error) {
        if (error === 'cancel') {
          ElMessage.info('已取消删除');
        } else {
          console.error('删除支付码错误:', error);
          ElMessage.error('删除支付码失败: ' + (error.message || '未知错误'));
        }
      } finally {
        deleteLoading.value = false;
      }
    };
    
    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }).replace(/\//g, '-');
    };
    
    // 显示重置确认对话框
    const showResetConfirmation = () => {
      resetDialogVisible.value = true;
      confirmationText.value = '';
    };
    
    // 执行系统重置
    const executeReset = async () => {
      if (confirmationText.value !== 'RESET') {
        ElMessage.error('请输入正确的确认文字');
        return;
      }
      
      try {
        resetLoading.value = true;
        
        // 再次确认
        await ElMessageBox.confirm(
          '您确定要执行系统重置操作吗？此操作将不可逆地删除大量数据！',
          '最终确认',
          {
            confirmButtonText: '确认重置',
            cancelButtonText: '取消',
            type: 'warning',
          }
        );
        
        // 调用API执行重置
        const response = await resetSystem();
        console.log('系统重置响应数据:', response);
        console.log('响应统计数据:', response.stats);
        console.log('删除统计数据:', response.stats?.deleted);
        
        // 确保响应包含必要的数据结构
        if (!response.stats) {
          response.stats = {};
        }
        if (!response.stats.deleted) {
          response.stats.deleted = {
            users: 0, exchanges: 0, announcements: 0,
            feedbacks: 0, notifications: 0, logs: 0
          };
        }
        if (!response.stats.updated) {
          response.stats.updated = { products: 0 };
        }
        
        resetResult.value = response;
        
        // 关闭确认对话框，显示结果对话框
        resetDialogVisible.value = false;
        resultDialogVisible.value = true;
        
        if (response.success) {
          ElMessage.success('系统重置成功');
        } else {
          ElMessage.error('系统重置失败: ' + response.message);
        }
      } catch (error) {
        if (error === 'cancel') {
          ElMessage.info('已取消操作');
        } else {
          console.error('系统重置错误:', error);
          ElMessage.error('系统重置失败: ' + (error.message || '未知错误'));
          
          resetResult.value = {
            success: false,
            error: error.message || '未知错误'
          };
        }
      } finally {
        resetLoading.value = false;
      }
    };
    
    // 获取清理统计数据，用于表格展示
    const getStatsData = () => {
      if (!resetResult.value || !resetResult.value.stats) {
        console.error('无效的重置结果或统计数据', resetResult.value);
        return [];
      }
      
      const { stats } = resetResult.value;
      console.log('构建表格数据, stats:', JSON.stringify(stats, null, 2));
      
      if (!stats.deleted) {
        console.error('删除统计数据不存在');
        return [];
      }
      
      const { deleted } = stats;
      console.log('删除的数据项:', JSON.stringify(deleted, null, 2));
      
      // 测试显示实际的数据（临时）
      // 如果数据全是 0，输出一些测试数据来验证表格是否正常显示
      let hasNonZeroValues = Object.values(deleted).some(val => val > 0);
      if (!hasNonZeroValues) {
        console.warn('所有删除统计数据都是0，可能是数据没有正确传递或者没有数据被删除');
      }
      
      return [
        { name: '用户账号', count: deleted.users || 0 },
        { name: '兑换订单', count: deleted.exchanges || 0 },
        { name: '系统公告', count: deleted.announcements || 0 },
        { name: '用户反馈', count: deleted.feedbacks || 0 },
        { name: '系统通知', count: deleted.notifications || 0 },
        { name: '系统日志', count: deleted.logs || 0 }
      ];
    };

    // 通知监控和统计相关状态
    const notificationStats = ref({ totalStats: { total: 0, success: 0, failed: 0, successRate: 0 } });
    const statsLoading = ref(false);
    const processingRetries = ref(false);
    const showHistoryDialog = ref(false);
    const historyFilters = ref({ notificationType: '', status: '', triggerSource: '', page: 1, pageSize: 10 });
    const notificationHistory = ref({ logs: [], pagination: { total: 0 } });
    const historyLoading = ref(false);
    const retryingNotifications = ref([]);
    const allNotificationTypes = ref([
      { type: 'exchange_notification', name: '兑换申请' },
      { type: 'stock_alert', name: '库存告警' },
      { type: 'new_user_welcome', name: '新用户欢迎' },
      { type: 'new_product_notification', name: '新品上架' },
      { type: 'order_alert', name: '订单预警' },
      { type: 'maintenance_notification', name: '维护通知' },
      { type: 'error_alert', name: '错误告警' }
    ]);

    // 加载通知统计数据
    const loadNotificationStats = async () => {
      try {
        statsLoading.value = true;
        const response = await api.get('/system/notification-stats');
        if (response.data.success) {
          notificationStats.value = response.data.data;
        }
      } catch (error) {
        ElMessage.error('加载通知统计数据失败: ' + (error.response?.data?.message || error.message));
      } finally {
        statsLoading.value = false;
      }
    };

    // 处理失败重试
    const processFailedRetries = async () => {
      try {
        processingRetries.value = true;
        const response = await api.post('/system/process-failed-retries');
        
        if (response.data.success) {
          ElMessage.success('失败重试处理成功');
        } else {
          ElMessage.error(response.data.message);
        }
      } catch (error) {
        ElMessage.error('处理失败重试失败: ' + (error.response?.data?.message || error.message));
      } finally {
        processingRetries.value = false;
      }
    };

    // 加载通知历史
    const loadNotificationHistory = async () => {
      try {
        historyLoading.value = true;
        const response = await api.get('/system/notification-history', {
          params: historyFilters.value
        });
        
        if (response.data.success) {
          notificationHistory.value = response.data.data;
        }
      } catch (error) {
        ElMessage.error('加载通知历史失败: ' + (error.response?.data?.message || error.message));
      } finally {
        historyLoading.value = false;
      }
    };

    // 获取通知类型名称
    const getNotificationTypeName = (notificationType) => {
      const config = notificationConfigs.value.find(c => c.notificationType === notificationType);
      return config ? config.typeName : notificationType;
    };

    // 获取通知状态标签类型
    const getStatusTagType = (status) => {
      const statusMap = {
        success: 'success',
        failed: 'error',
        pending: 'info'
      };
      return statusMap[status] || 'info';
    };

    // 获取通知状态文本
    const getStatusText = (status) => {
      const statusMap = {
        success: '发送成功',
        failed: '发送失败',
        pending: '等待重试'
      };
      return statusMap[status] || status;
    };

    // 获取通知触发源标签类型
    const getTriggerTagType = (triggerSource) => {
      const sourceMap = {
        auto: 'success',
        manual: 'info',
        test: 'warning'
      };
      return sourceMap[triggerSource] || 'info';
    };

    // 获取通知触发源文本
    const getTriggerText = (triggerSource) => {
      const sourceMap = {
        auto: '自动触发',
        manual: '手动发送',
        test: '测试发送'
      };
      return sourceMap[triggerSource] || triggerSource;
    };

    // 重试通知
    const retryNotification = async (id) => {
      try {
        retryingNotifications.value.push(id);
        const response = await api.post(`/system/retry-notification/${id}`);
        
        if (response.data.success) {
          ElMessage.success('重试成功');
          await loadNotificationHistory();
        } else {
          ElMessage.error(response.data.message);
        }
      } catch (error) {
        ElMessage.error('重试失败: ' + (error.response?.data?.message || error.message));
      } finally {
        retryingNotifications.value = retryingNotifications.value.filter(i => i !== id);
      }
    };

    // 监听webhookConfigured状态变化
    watch(webhookConfigured, (newValue, oldValue) => {
      console.log('🔧 webhookConfigured状态变化:', oldValue, '->', newValue);
    });

    onMounted(async () => {
      console.log('🔧 SystemSettings页面初始化...');

      // 先初始化状态
      webhookConfigured.value = false;
      console.log('🔧 初始化webhookConfigured为false');

      // 加载各种配置
      loadPaymentQRCode();
      
      // 尝试加载通知配置
      console.log('🔧 开始调用loadNotificationConfigs...');
      await loadNotificationConfigs();
      console.log('🔧 loadNotificationConfigs调用完成，当前状态:', webhookConfigured.value);
      
      // 如果没有配置或webhookConfigured为false，尝试测试连接
      if (notificationConfigs.value.length === 0 || !webhookConfigured.value) {
        console.log('🔧 配置可能不完整，尝试测试Webhook连接...');
        try {
          const response = await api.post('/system/test-webhook');
          if (response.data.success) {
            console.log('✅ Webhook连接测试成功，设置webhookConfigured为true');
            webhookConfigured.value = true;
            
            // 如果配置为空，重新加载配置
            if (notificationConfigs.value.length === 0) {
              console.log('🔧 配置为空，重新加载配置...');
              await loadNotificationConfigs();
            }
          }
        } catch (error) {
          console.error('❌ Webhook连接测试失败:', error);
        }
      }
      
      // 确保始终有配置项可用
      if (notificationConfigs.value.length === 0) {
        console.log('⚠️ 仍然没有配置项，将使用计算属性中的默认配置');
      }

      loadNotificationStats();

      console.log('🔧 SystemSettings页面初始化完成，最终Webhook状态:', webhookConfigured.value);
    });
    
    return {
      // 重置相关
      resetLoading,
      resetDialogVisible,
      resultDialogVisible,
      confirmationText,
      resetResult,
      showResetConfirmation,
      executeReset,
      getStatsData,
      
      // 支付码相关
      paymentQRCode,
      qrcodeLoading,
      uploadLoading,
      deleteLoading,
      uploadRef,
      beforeQRCodeUpload,
      customUploadRequest,
      handleDeleteQRCode,
      formatDate,
      
      // 飞书群消息管理相关
      webhookConfigured,
      testingConnection,
      configsLoading,
      sendingCustomMessage,
      notificationConfigs,
      updatingConfigs,
      sendingTests,
      customMessage,
      reportTypes,
      businessTypes,
      systemTypes,
      businessConfigs,
      reportConfigs,
      systemConfigs,
      loadNotificationConfigs,
      updateNotificationConfig,
      testWebhookConnection,
      sendTestNotification,
      sendCustomMessage,
      isConfigEnabled,

      // Webhook配置相关
      webhookConfigDialogVisible,
      configuringWebhook,
      webhookFormRef,
      webhookForm,
      webhookRules,
      showWebhookConfigDialog,
      saveWebhookConfig,
      
      // 通知监控和统计相关
      notificationStats,
      statsLoading,
      processingRetries,
      showHistoryDialog,
      historyFilters,
      notificationHistory,
      historyLoading,
      retryingNotifications,
      allNotificationTypes,
      loadNotificationStats,
      processFailedRetries,
      loadNotificationHistory,
      getNotificationTypeName,
      getStatusTagType,
      getStatusText,
      getTriggerTagType,
      getTriggerText,
      retryNotification
    };
  }
};
</script>

<style scoped>
.system-settings {
  padding: 20px;
}

.main-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  flex-direction: column;
}

.subtitle {
  color: #909399;
  margin-top: 5px;
  font-size: 14px;
}

.module-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.module-title i {
  margin-right: 8px;
}

.inner-card {
  margin: 10px 0;
}

.card-content {
  padding: 10px 0;
}

.warning-text {
  color: #f56c6c;
  background-color: #fef0f0;
  padding: 10px;
  border-radius: 4px;
  margin: 15px 0;
}

.actions {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.reset-confirmation {
  margin-bottom: 20px;
}

.confirmation-details {
  margin: 20px 0;
}

.confirmation-details h4 {
  margin: 15px 0 10px;
  color: #303133;
}

.confirmation-details ul {
  margin: 10px 0;
  padding-left: 20px;
}

.confirmation-details li {
  margin-bottom: 5px;
  color: #606266;
}

.confirmation-input {
  margin-top: 20px;
}

.confirmation-input p {
  margin-bottom: 10px;
  font-weight: bold;
}

.reset-result {
  margin-bottom: 20px;
}

.result-stats {
  margin-top: 20px;
}

.result-stats h4 {
  margin: 15px 0 10px;
  color: #303133;
}

.error-message {
  margin-top: 20px;
  color: #f56c6c;
  background-color: #fef0f0;
  padding: 15px;
  border-radius: 4px;
}

.mt-20 {
  margin-top: 20px;
}

.description-text {
  color: #606266;
  margin-bottom: 15px;
}

.qrcode-management {
  margin: 20px 0;
}

.current-qrcode h4,
.upload-qrcode h4 {
  margin-bottom: 15px;
  color: #303133;
  font-size: 16px;
}

.qrcode-preview {
  display: flex;
  gap: 20px;
}

.qrcode-image {
  width: 200px;
  height: 200px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.qrcode-info {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.no-qrcode {
  text-align: center;
}

.image-placeholder {
  width: 200px;
  height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  color: #909399;
  margin: 0 auto;
}

.image-placeholder .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.notice-text {
  margin-top: 10px;
  color: #909399;
}

.qrcode-uploader {
  margin-top: 15px;
}

.upload-qrcode .el-upload__tip {
  line-height: 1.5;
  margin-top: 8px;
}

.section-container {
  margin-bottom: 20px;
}

.basic-config {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.config-info {
  display: flex;
  flex-direction: column;
}

.config-label {
  font-weight: bold;
  margin-bottom: 5px;
}

.config-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.notification-configs {
  margin-top: 15px;
}

.config-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.config-category {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.config-category h5 {
  margin: 0 0 15px 0;
  font-size: 14px;
  font-weight: 600;
  color: #606266;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.config-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.config-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  margin-bottom: 8px;
  min-height: 46px;  /* 确保即使内容少也有最小高度 */
}

.config-item .config-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
  overflow: hidden; /* 防止超长文本溢出 */
}

.config-name {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.config-type {
  color: #909399;
  font-size: 12px;
}

.config-schedule {
  color: #67c23a;
  font-size: 12px;
  font-weight: 500;
}

/* 确保开关控件有足够的右边距 */
.config-item .el-switch {
  margin-left: 10px;
  min-width: 40px; /* 确保开关有最小宽度 */
}

/* 强制显示开关控件 */
.config-item .el-switch {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 1 !important;
}

/* 确保开关的点击区域正常 */
.config-item :deep(.el-switch__core) {
  cursor: pointer;
  display: inline-block !important;
  position: relative !important;
  visibility: visible !important;
}

/* 确保开关的内部元素可见 */
.config-item :deep(.el-switch__label) {
  visibility: visible !important;
  display: inline-block !important;
}

.manual-send {
  margin-top: 20px;
}

.send-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.send-category {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.send-category h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #606266;
}

.send-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.send-buttons .el-button {
  margin: 0;
  flex: 1;
  min-width: auto;
}

.custom-message {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.custom-message-actions {
  margin-top: 12px;
  display: flex;
  justify-content: flex-end;
}

/* 飞书群高级管理功能样式 */
.advanced-features {
  margin-top: 20px;
}

.feature-card {
  height: 100%;
  transition: all 0.3s;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.feature-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
}

.feature-description {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.router-link-active {
  text-decoration: none;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .config-categories,
  .send-categories {
    grid-template-columns: 1fr;
  }
  
  .basic-config {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .send-buttons {
    flex-direction: column;
  }
  
  .send-buttons .el-button {
    width: 100%;
  }
}

.stats-cards {
  display: flex;
  gap: 20px;
}

.stats-card {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  flex: 1;
}

.stats-icon {
  font-size: 24px;
  margin-bottom: 10px;
}

.stats-content {
  display: flex;
  flex-direction: column;
}

.stats-number {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 5px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.monitor-actions {
  margin-top: 20px;
  display: flex;
  gap: 10px;
}

.history-container {
  padding: 20px;
}

.history-filters {
  margin-bottom: 20px;
}

.history-filters .el-row {
  margin-bottom: 16px;
}

.history-filters .el-col {
  margin-bottom: 8px;
}

.history-filters .el-select {
  width: 100%;
}

.history-filters .el-button {
  width: 100%;
}

.history-pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.error-text {
  color: #f56c6c;
}

.webhook-warning {
  margin-bottom: 15px;
}

.no-configs {
  text-align: center;
  padding: 30px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-top: 15px;
}

.no-configs .el-button {
  margin-top: 15px;
}

.empty-configs {
  text-align: center;
  margin-top: 10px;
  color: #909399;
}
</style> 